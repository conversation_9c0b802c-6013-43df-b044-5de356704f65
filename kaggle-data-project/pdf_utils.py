"""
PDF Utilities Module

This module contains reusable functions for PDF text extraction, dataset ID identification,
and text processing that can be used across multiple scripts.
"""

import pandas as pd
import re
import random
from pathlib import Path
from pypdf import PdfReader

# For English word validation
try:
    import nltk
    from nltk.corpus import words
    # Download words corpus if not already present
    try:
        nltk.data.find('corpora/words')
    except LookupError:
        print("Downloading NLTK words corpus...")
        nltk.download('words', quiet=True)

    # Load English words set
    english_words = set(word.lower() for word in words.words())
    ENGLISH_DICT_AVAILABLE = True
except ImportError:
    print("NLTK not available. Install with: pip install nltk")
    ENGLISH_DICT_AVAILABLE = False
    english_words = set()
except Exception as e:
    print(f"Error loading English dictionary: {e}")
    ENGLISH_DICT_AVAILABLE = False
    english_words = set()


def clean_text_for_urls(text):
    """Clean text to handle URLs split across lines and other formatting issues."""
    # Fix hyphenated words split across lines (remove hyphen and join words)
    text = re.sub(r'(\w+)-\s*\n\s*(\w+)', r'\1\2', text)

    # Fix common URL splits
    text = re.sub(r'https://doi\.\s*\n\s*org/', 'https://doi.org/', text)
    text = re.sub(r'doi\.org/\s*\n\s*', 'doi.org/', text)
    text = re.sub(r'https://doi\.\s+org/', 'https://doi.org/', text)
    text = re.sub(r'10\.\d+/\s*\n\s*', '10.', text)

    # Fix dryad URLs specifically
    text = re.sub(r'dryad\.\s*\n\s*', 'dryad.', text)
    text = re.sub(r'10\.5061/\s*\n\s*dryad\.', '10.5061/dryad.', text)

    # Fix other common splits in dataset IDs
    text = re.sub(r'(\w+)\s*\n\s*(\w+)', r'\1\2', text)
    text = re.sub(r'(\d+)\s*\n\s*(\d+)', r'\1\2', text)

    # Remove excessive whitespace but preserve single spaces
    text = re.sub(r'\s+', ' ', text)

    return text


def extract_text_from_pdf(pdf_path, max_pages=None):
    """
    Extract text from a PDF file with error handling.
    
    Args:
        pdf_path (str): Path to the PDF file
        max_pages (int): Maximum number of pages to process (None for all)
        
    Returns:
        str: Extracted text from the PDF
    """
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            text = ""
            
            # Determine number of pages to process
            num_pages = len(pdf_reader.pages)
            if max_pages:
                num_pages = min(num_pages, max_pages)
            
            for i in range(num_pages):
                try:
                    page_text = pdf_reader.pages[i].extract_text()
                    text += page_text + "\n"
                except Exception as page_error:
                    print(f"Error extracting page {i+1} from {pdf_path}: {page_error}")
                    continue
                    
        return text
    except Exception as e:
        print(f"Error extracting text from {pdf_path}: {e}")
        return ""


def is_doi_dataset_id(dataset_id):
    """
    Check if a dataset ID is a DOI (starts with https://doi.org).
    
    Args:
        dataset_id (str): The dataset ID to check
        
    Returns:
        bool: True if it's a DOI, False otherwise
    """
    if pd.isna(dataset_id) or dataset_id == '' or dataset_id == 'Missing':
        return False
    
    return str(dataset_id).startswith('https://doi.org')


def is_non_doi_dataset_id(dataset_id):
    """
    Check if a dataset ID is a non-DOI identifier.
    
    Args:
        dataset_id (str): The dataset ID to check
        
    Returns:
        bool: True if it's a non-DOI identifier, False otherwise
    """
    if pd.isna(dataset_id) or dataset_id == '' or dataset_id == 'Missing':
        return False
    
    # Not a DOI and not missing/empty
    return not str(dataset_id).startswith('https://doi.org')


def classify_dataset_id(dataset_id):
    """
    Classify a dataset ID as DOI, non-DOI, or missing.
    
    Args:
        dataset_id (str): The dataset ID to classify
        
    Returns:
        str: 'DOI', 'non-DOI', or 'Missing'
    """
    if pd.isna(dataset_id) or dataset_id == '' or dataset_id == 'Missing':
        return 'Missing'
    elif str(dataset_id).startswith('https://doi.org'):
        return 'DOI'
    else:
        return 'non-DOI'


def clean_and_extract_words(text, num_words=50, min_length=4):
    """
    Clean text and extract English words with specified minimum length.
    
    Args:
        text (str): Input text to process
        num_words (int): Number of words to extract
        min_length (int): Minimum word length
        
    Returns:
        list: List of cleaned English words
    """
    if not text:
        return []
    
    # Convert to lowercase
    text = text.lower()
    
    # Remove URLs, DOIs, and other patterns
    text = re.sub(r'https?://[^\s]+', '', text)
    text = re.sub(r'doi[:\s]*[^\s]+', '', text)
    text = re.sub(r'www\.[^\s]+', '', text)
    
    # Remove special characters and numbers, keep only letters and spaces
    text = re.sub(r'[^a-z\s]', ' ', text)
    
    # Split into words and filter for minimum length
    words = [word.strip() for word in text.split() if len(word.strip()) >= min_length]
    
    # Filter for English words if dictionary is available
    if ENGLISH_DICT_AVAILABLE:
        words = [word for word in words if word in english_words]
    
    # Return the specified number of words
    return words[:num_words]


def find_dataset_ids_in_text(text):
    """
    Find potential dataset IDs in text using specific patterns.
    Focus on actual dataset repositories and avoid false positives.

    Args:
        text (str): Text to search for dataset IDs

    Returns:
        list: List of potential dataset IDs found
    """
    dataset_ids = []

    # Clean text first
    cleaned_text = clean_text_for_urls(text)

    # DOI patterns - more specific
    doi_patterns = [
        r'https://doi\.org/10\.\d+/[^\s\)\.,;]+',
        r'doi\.org/10\.\d+/[^\s\)\.,;]+',
        r'doi:\s*https://doi\.org/10\.\d+/[^\s\)\.,;]+',
        r'doi:\s*10\.\d+/[^\s\)\.,;]+',
    ]

    for pattern in doi_patterns:
        matches = re.findall(pattern, cleaned_text, re.IGNORECASE)
        for match in matches:
            # Clean up the match
            clean_match = match.strip('.,;:)')
            if not clean_match.startswith('https://'):
                if clean_match.startswith('doi.org/'):
                    clean_match = 'https://' + clean_match
                elif clean_match.startswith('doi:'):
                    clean_match = clean_match.replace('doi:', '').strip()
                    if not clean_match.startswith('https://'):
                        clean_match = 'https://doi.org/' + clean_match.replace('doi.org/', '')
                else:
                    clean_match = 'https://doi.org/' + clean_match
            dataset_ids.append(clean_match)

    # Non-DOI patterns (specific database identifiers only)
    non_doi_patterns = [
        r'\bCHEMBL\d+\b',           # ChEMBL database
        r'\bIPR\d{6}\b',            # InterPro
        r'\bPF\d{5}\b',             # Pfam
        r'\bGSE\d+\b',              # Gene Expression Omnibus
        r'\bSRP\d+\b',              # Sequence Read Archive Project
        r'\bEMPIAR-\d+\b',          # Electron Microscopy Public Image Archive
        r'\bENSBTAG\d+\b',          # Ensembl gene IDs
        r'\bSAMN\d+\b',             # BioSample
        r'\bEPI\d+\b',              # GISAID
        r'\bSRR\d+\b',              # Sequence Read Archive Run
        r'\bERR\d+\b',              # European Nucleotide Archive Run
        r'\bPRJNA\d+\b',            # BioProject
        r'\bCVCL_\d+\b',            # Cell line identifiers
        r'\b[A-Z]\d[A-Z0-9]{3}\b',  # PDB codes like 5VA1
        r'\bMODEL\d+\b',            # Model identifiers
    ]

    for pattern in non_doi_patterns:
        matches = re.findall(pattern, cleaned_text, re.IGNORECASE)
        dataset_ids.extend(matches)

    # Filter out obvious false positives
    filtered_ids = []
    for dataset_id in dataset_ids:
        # Skip if it's just a year or common number
        if re.match(r'^\d{4}$', dataset_id):  # Years
            continue
        if re.match(r'^\d{1,3}$', dataset_id):  # Small numbers
            continue
        if len(dataset_id) < 4:  # Too short
            continue
        filtered_ids.append(dataset_id)

    return list(set(filtered_ids))  # Remove duplicates


def filter_meaningful_words(words, target_parts=None):
    """Filter words to keep only meaningful contextual text."""
    if not words:
        return []

    # Words to avoid (isolated numbers, URLs, etc.)
    avoid_patterns = [
        r'^\d+$',  # Isolated numbers
        r'^[IVX]+$',  # Roman numerals
        r'^(https?|doi|www|org|com|net)$',  # URL parts
        r'^(fig|figure|table|ref|references?)$',  # Figure/table references
        r'^[a-z]$',  # Single letters
        r'^\d{4}$',  # Years
        r'^(pp?|vol|no|issue)$',  # Publication terms
    ]

    # If we have target parts, avoid them too
    if target_parts:
        for part in target_parts:
            if len(part) > 2:  # Only meaningful parts
                avoid_patterns.append(rf'^{re.escape(part)}$')

    filtered_words = []
    for word in words:
        word_lower = word.lower()
        # Check if word should be avoided
        should_avoid = False
        for pattern in avoid_patterns:
            if re.match(pattern, word_lower, re.IGNORECASE):
                should_avoid = True
                break

        if not should_avoid:
            filtered_words.append(word)

    return filtered_words


def extract_preceding_words_from_text(text, target, num_words=50):
    """
    Extract preceding words before a target in text.
    
    Args:
        text (str): Text to search in
        target (str): Target string to find
        num_words (int): Number of preceding words to extract
        
    Returns:
        list: List of preceding English words
    """
    # Clean text first
    cleaned_text = clean_text_for_urls(text)
    
    # Find the position of the target in the text
    text_lower = cleaned_text.lower()
    target_lower = str(target).lower()
    
    # Try to find the target
    pos = text_lower.find(target_lower)
    if pos == -1:
        # Try partial matches for DOIs
        if target_lower.startswith('https://doi.org/'):
            partial_target = target_lower.replace('https://doi.org/', '')
            pos = text_lower.find(partial_target)
        elif 'doi.org/' in target_lower:
            partial_target = target_lower.split('doi.org/', 1)[1]
            pos = text_lower.find(partial_target)
    
    if pos == -1:
        return []
    
    # Extract text before the target
    preceding_text = cleaned_text[:pos]
    
    # Extract and clean words
    return clean_and_extract_words(preceding_text, num_words)

#!/usr/bin/env python3
"""
Word Frequency Analysis Script

This script analyzes the partial_extraction.csv file to find the most frequent:
- Single words (4+ characters)
- Two contiguous words (bigrams)
- Three contiguous words (trigrams)

in the preceding_words column for each type (Missing, Primary, Secondary).
It excludes cases where dataset extraction could not find the dataset.

Features:
- Filters for English words only (using NLTK corpus) when available
- Removes URLs, DOIs, and special characters
- Analyzes words with 4+ characters for single words
- Provides detailed statistics and filtering information
- Generates n-gram analysis (bigrams and trigrams)
"""

import pandas as pd
import re
from collections import Counter
import os

# For English word validation
try:
    import nltk
    from nltk.corpus import words
    # Download words corpus if not already present
    try:
        nltk.data.find('corpora/words')
    except LookupError:
        print("Downloading NLTK words corpus...")
        nltk.download('words', quiet=True)

    # Load English words set
    english_words = set(word.lower() for word in words.words())
    ENGLISH_DICT_AVAILABLE = True
    print(f"Loaded {len(english_words)} English words from NLTK")
except ImportError:
    print("NLTK not available. Install with: pip install nltk")
    print("Will proceed without English word filtering.")
    ENGLISH_DICT_AVAILABLE = False
    english_words = set()
except Exception as e:
    print(f"Error loading English dictionary: {e}")
    print("Will proceed without English word filtering.")
    ENGLISH_DICT_AVAILABLE = False
    english_words = set()

def clean_and_tokenize_text(text, english_only=True):
    """
    Clean text and extract words with 4+ characters, optionally filtering for English words.

    Args:
        text (str): Input text to process
        english_only (bool): If True, only include words that exist in English dictionary

    Returns:
        list: List of cleaned words with 4+ characters (and optionally English words only)
    """
    if pd.isna(text) or text == '':
        return []

    # Convert to lowercase
    text = text.lower()

    # Remove URLs, DOIs, and other patterns
    text = re.sub(r'https?://[^\s]+', '', text)
    text = re.sub(r'doi[:\s]*[^\s]+', '', text)
    text = re.sub(r'www\.[^\s]+', '', text)

    # Remove special characters and numbers, keep only letters and spaces
    text = re.sub(r'[^a-z\s]', ' ', text)

    # Split into words and filter for 4+ characters
    words = [word.strip() for word in text.split() if len(word.strip()) >= 4]

    # Filter for English words if dictionary is available and requested
    if english_only and ENGLISH_DICT_AVAILABLE:
        words = [word for word in words if word in english_words]

    return words

def extract_ngrams(words, n):
    """
    Extract n-grams from a list of words.

    Args:
        words (list): List of words
        n (int): Size of n-gram (2 for bigrams, 3 for trigrams)

    Returns:
        list: List of n-grams as tuples
    """
    if len(words) < n:
        return []

    ngrams = []
    for i in range(len(words) - n + 1):
        ngram = tuple(words[i:i + n])
        ngrams.append(ngram)

    return ngrams

def analyze_word_frequencies(csv_file_path):
    """
    Analyze word frequencies in the partial_extraction.csv file.
    
    Args:
        csv_file_path (str): Path to the CSV file
    """
    # Read the CSV file
    try:
        df = pd.read_csv(csv_file_path)
        print(f"Loaded {len(df)} records from {csv_file_path}")
    except FileNotFoundError:
        print(f"Error: File {csv_file_path} not found.")
        return
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return
    
    # Check required columns
    required_columns = ['article_id', 'dataset_id', 'preceding_words', 'type']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"Error: Missing required columns: {missing_columns}")
        return
    
    # Filter out cases where dataset extraction failed
    # We exclude rows where dataset_id is 'Missing' or contains 'DATASET_ID_NOT_FOUND'
    original_count = len(df)
    df_filtered = df[
        (df['dataset_id'] != 'Missing') & 
        (~df['dataset_id'].str.contains('DATASET_ID_NOT_FOUND', na=False))
    ].copy()
    
    print(f"Filtered out {original_count - len(df_filtered)} records where dataset extraction failed")
    print(f"Analyzing {len(df_filtered)} records with successful dataset extraction")
    
    # Get unique types
    types = df_filtered['type'].unique()
    print(f"Found types: {list(types)}")
    
    # Analyze word frequencies for each type
    results = {}

    for data_type in types:
        print(f"\nAnalyzing type: {data_type}")

        # Filter data for current type
        type_data = df_filtered[df_filtered['type'] == data_type]
        print(f"  Records for {data_type}: {len(type_data)}")

        # Collect all words for this type
        all_words = []
        all_words_no_filter = []
        all_bigrams = []
        all_trigrams = []

        for _, row in type_data.iterrows():
            # Get words with English filtering
            words_english = clean_and_tokenize_text(row['preceding_words'], english_only=True)
            all_words.extend(words_english)

            # Extract bigrams and trigrams from English words
            bigrams = extract_ngrams(words_english, 2)
            trigrams = extract_ngrams(words_english, 3)
            all_bigrams.extend(bigrams)
            all_trigrams.extend(trigrams)

            # Also get words without filtering for comparison
            words_all = clean_and_tokenize_text(row['preceding_words'], english_only=False)
            all_words_no_filter.extend(words_all)

        if ENGLISH_DICT_AVAILABLE:
            print(f"  Total words extracted (all): {len(all_words_no_filter)}")
            print(f"  Total words extracted (English only): {len(all_words)}")
            print(f"  Filtered out {len(all_words_no_filter) - len(all_words)} non-English words")
        else:
            print(f"  Total words extracted: {len(all_words)}")
            print("  (No English filtering applied - NLTK not available)")

        # Count frequencies
        word_counts = Counter(all_words)
        bigram_counts = Counter(all_bigrams)
        trigram_counts = Counter(all_trigrams)

        # Get top results
        top_50_words = word_counts.most_common(50)
        top_30_bigrams = bigram_counts.most_common(30)
        top_20_trigrams = trigram_counts.most_common(20)

        results[data_type] = {
            'record_count': len(type_data),
            'total_words': len(all_words),
            'unique_words': len(word_counts),
            'total_bigrams': len(all_bigrams),
            'unique_bigrams': len(bigram_counts),
            'total_trigrams': len(all_trigrams),
            'unique_trigrams': len(trigram_counts),
            'top_50_words': top_50_words,
            'top_30_bigrams': top_30_bigrams,
            'top_20_trigrams': top_20_trigrams
        }

        print(f"  Unique words: {len(word_counts)}")
        print(f"  Unique bigrams: {len(bigram_counts)}")
        print(f"  Unique trigrams: {len(trigram_counts)}")
        print(f"  Top 5 words: {top_50_words[:5]}")
        print(f"  Top 5 bigrams: {[(' '.join(bg), count) for bg, count in top_30_bigrams[:5]]}")
        print(f"  Top 5 trigrams: {[(' '.join(tg), count) for tg, count in top_20_trigrams[:5]]}")
    
    # Save results to file
    output_file = 'results/word_frequency_analysis.txt'
    os.makedirs('results', exist_ok=True)

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("Word Frequency Analysis Results\n")
        f.write("=" * 70 + "\n\n")
        f.write(f"Analysis of: {csv_file_path}\n")
        f.write(f"Total records analyzed: {len(df_filtered)}\n")
        f.write(f"Records excluded (failed extraction): {original_count - len(df_filtered)}\n")
        if ENGLISH_DICT_AVAILABLE:
            f.write(f"English word filtering: ENABLED (using NLTK with {len(english_words)} words)\n")
        else:
            f.write("English word filtering: DISABLED (NLTK not available)\n")
        f.write("\n")

        for data_type in sorted(results.keys()):
            f.write(f"\nTYPE: {data_type.upper()}\n")
            f.write("=" * 50 + "\n")
            f.write(f"Records: {results[data_type]['record_count']}\n")
            f.write(f"Total words: {results[data_type]['total_words']}\n")
            f.write(f"Unique words: {results[data_type]['unique_words']}\n")
            f.write(f"Total bigrams: {results[data_type]['total_bigrams']}\n")
            f.write(f"Unique bigrams: {results[data_type]['unique_bigrams']}\n")
            f.write(f"Total trigrams: {results[data_type]['total_trigrams']}\n")
            f.write(f"Unique trigrams: {results[data_type]['unique_trigrams']}\n\n")

            # Single words
            f.write("Top 50 Most Frequent Words (4+ characters):\n")
            f.write("-" * 50 + "\n")
            f.write("Rank | Word                 | Frequency\n")
            f.write("-----|----------------------|----------\n")
            for rank, (word, count) in enumerate(results[data_type]['top_50_words'], 1):
                f.write(f"{rank:4d} | {word:20s} | {count:6d}\n")
            f.write("\n")

            # Bigrams
            f.write("Top 30 Most Frequent Bigrams (2 contiguous words):\n")
            f.write("-" * 50 + "\n")
            f.write("Rank | Bigram                           | Frequency\n")
            f.write("-----|----------------------------------|----------\n")
            for rank, (bigram, count) in enumerate(results[data_type]['top_30_bigrams'], 1):
                bigram_str = ' '.join(bigram)
                f.write(f"{rank:4d} | {bigram_str:32s} | {count:6d}\n")
            f.write("\n")

            # Trigrams
            f.write("Top 20 Most Frequent Trigrams (3 contiguous words):\n")
            f.write("-" * 50 + "\n")
            f.write("Rank | Trigram                                      | Frequency\n")
            f.write("-----|----------------------------------------------|----------\n")
            for rank, (trigram, count) in enumerate(results[data_type]['top_20_trigrams'], 1):
                trigram_str = ' '.join(trigram)
                f.write(f"{rank:4d} | {trigram_str:44s} | {count:6d}\n")
            f.write("\n" + "=" * 70 + "\n")

    print(f"\nResults saved to: {output_file}")

    # Create CSV summaries for each type of analysis
    os.makedirs('results', exist_ok=True)

    # Single words CSV
    words_csv_file = 'results/word_frequency_summary.csv'
    words_data = []
    for data_type in sorted(results.keys()):
        for rank, (word, count) in enumerate(results[data_type]['top_50_words'], 1):
            words_data.append({
                'type': data_type,
                'rank': rank,
                'word': word,
                'frequency': count
            })
    words_df = pd.DataFrame(words_data)
    words_df.to_csv(words_csv_file, index=False)
    print(f"Words CSV summary saved to: {words_csv_file}")

    # Bigrams CSV
    bigrams_csv_file = 'results/bigram_frequency_summary.csv'
    bigrams_data = []
    for data_type in sorted(results.keys()):
        for rank, (bigram, count) in enumerate(results[data_type]['top_30_bigrams'], 1):
            bigrams_data.append({
                'type': data_type,
                'rank': rank,
                'bigram': ' '.join(bigram),
                'frequency': count
            })
    bigrams_df = pd.DataFrame(bigrams_data)
    bigrams_df.to_csv(bigrams_csv_file, index=False)
    print(f"Bigrams CSV summary saved to: {bigrams_csv_file}")

    # Trigrams CSV
    trigrams_csv_file = 'results/trigram_frequency_summary.csv'
    trigrams_data = []
    for data_type in sorted(results.keys()):
        for rank, (trigram, count) in enumerate(results[data_type]['top_20_trigrams'], 1):
            trigrams_data.append({
                'type': data_type,
                'rank': rank,
                'trigram': ' '.join(trigram),
                'frequency': count
            })
    trigrams_df = pd.DataFrame(trigrams_data)
    trigrams_df.to_csv(trigrams_csv_file, index=False)
    print(f"Trigrams CSV summary saved to: {trigrams_csv_file}")

    # Print summary statistics
    print(f"\nSUMMARY STATISTICS:")
    print("=" * 80)
    print(f"{'Type':<10} {'Records':<8} {'Words':<8} {'Unique':<8} {'Bigrams':<9} {'Trigrams':<9}")
    print("-" * 80)
    for data_type in sorted(results.keys()):
        print(f"{data_type:<10} {results[data_type]['record_count']:<8} "
              f"{results[data_type]['total_words']:<8} {results[data_type]['unique_words']:<8} "
              f"{results[data_type]['unique_bigrams']:<9} {results[data_type]['unique_trigrams']:<9}")

def main():
    """Main function to run the analysis."""
    csv_file_path = 'results/partial_extraction.csv'
    
    if not os.path.exists(csv_file_path):
        print(f"Error: File {csv_file_path} not found.")
        print("Please make sure the partial_extraction.csv file exists in the results directory.")
        return
    
    analyze_word_frequencies(csv_file_path)

if __name__ == "__main__":
    main()
